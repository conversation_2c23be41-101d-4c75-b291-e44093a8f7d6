// Ultra-minimal Firebase Functions for Hive Campus with CORS Support
const functions = require('firebase-functions/v1');

// Test function with CORS
exports.testFunction = functions
  .runWith({ memory: '128MB', timeoutSeconds: 30 })
  .https.onRequest((req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    res.json({
      success: true,
      message: 'Hive Campus Functions deployed successfully - CORS Fixed',
      timestamp: new Date().toISOString(),
      version: '7.0.0-MINIMAL'
    });
  });

// Admin PIN verification function
exports.verifyAdminPin = functions
  .runWith({ memory: '128MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Lazy load admin
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      const { pin } = req.body;

      if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
        res.status(400).json({ error: 'PIN must be exactly 8 digits' });
        return;
      }

      // Verify user is admin
      const userDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        res.status(403).json({ error: 'Only admin users can verify PIN' });
        return;
      }

      // Get stored PIN hash
      const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
      if (!securityDoc.exists || !securityDoc.data()?.adminPin) {
        res.status(404).json({ error: 'Admin PIN not set. Please set up your PIN first.' });
        return;
      }

      // Hash the provided PIN and compare
      const crypto = require('crypto');
      const hashedPin = crypto.createHash('sha256').update(pin).digest('hex');
      const storedPin = securityDoc.data()?.adminPin;

      if (hashedPin !== storedPin) {
        res.status(403).json({ error: 'Invalid PIN' });
        return;
      }

      res.status(200).json({ success: true, message: 'PIN verified successfully' });
    } catch (error) {
      console.error('Error verifying admin PIN:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Get wallet data function
exports.getWalletData = functions
  .runWith({ memory: '128MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Lazy load admin
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      const userId = decodedToken.uid;

      const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();

      if (!walletDoc.exists) {
        // Initialize wallet if it doesn't exist
        const walletData = {
          userId,
          balance: 0,
          referralCode: `user${userId.substring(0, 6)}`,
          usedReferral: false,
          history: [],
          grantedBy: 'system',
          createdAt: admin.firestore.Timestamp.now(),
          lastUpdated: admin.firestore.Timestamp.now()
        };

        await admin.firestore().collection('wallets').doc(userId).set(walletData);
        res.status(200).json(walletData);
        return;
      }

      res.status(200).json(walletDoc.data());
    } catch (error) {
      console.error('Error getting wallet data:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Get Stripe Connect account status
exports.getStripeConnectAccountStatus = functions
  .runWith({ memory: '128MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Lazy load admin
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      const userId = decodedToken.uid;

      // Get user's Stripe Connect account ID from Firestore
      const userDoc = await admin.firestore().collection('users').doc(userId).get();
      if (!userDoc.exists) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      const userData = userDoc.data();
      const stripeAccountId = userData?.stripeAccountId;

      if (!stripeAccountId) {
        res.status(200).json({
          hasAccount: false,
          needsOnboarding: true,
          canAcceptPayments: false
        });
        return;
      }

      res.status(200).json({
        hasAccount: true,
        needsOnboarding: false,
        canAcceptPayments: true,
        accountId: stripeAccountId
      });
    } catch (error) {
      console.error('Error getting Stripe Connect account status:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Grant wallet credits (admin only)
exports.grantWalletCredits = functions
  .runWith({ memory: '128MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Lazy load admin
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      const { userId, amount, description } = req.body;

      // Verify user is admin
      const adminDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();
      if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
        res.status(403).json({ error: 'Only admin users can grant credits' });
        return;
      }

      if (!userId || !amount || amount <= 0) {
        res.status(400).json({ error: 'Invalid userId or amount' });
        return;
      }

      // Get or create wallet
      const walletRef = admin.firestore().collection('wallets').doc(userId);
      const walletDoc = await walletRef.get();

      let currentBalance = 0;
      if (walletDoc.exists) {
        currentBalance = walletDoc.data()?.balance || 0;
      }

      const newBalance = currentBalance + amount;
      const transaction = {
        id: admin.firestore().collection('temp').doc().id,
        type: 'admin_grant',
        amount: amount,
        description: description || 'Admin credit grant',
        timestamp: admin.firestore.Timestamp.now(),
        grantedBy: decodedToken.uid,
        balanceAfter: newBalance
      };

      // Update wallet
      await walletRef.set({
        userId,
        balance: newBalance,
        referralCode: walletDoc.exists ? walletDoc.data()?.referralCode : `user${userId.substring(0, 6)}`,
        usedReferral: walletDoc.exists ? walletDoc.data()?.usedReferral : false,
        history: admin.firestore.FieldValue.arrayUnion(transaction),
        lastUpdated: admin.firestore.Timestamp.now(),
        grantedBy: decodedToken.uid
      }, { merge: true });

      res.status(200).json({
        success: true,
        message: `Granted $${amount} to user ${userId}`,
        newBalance
      });
    } catch (error) {
      console.error('Error granting wallet credits:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Get seller pending payouts
exports.getSellerPendingPayouts = functions
  .runWith({ memory: '128MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Lazy load admin
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      const userId = decodedToken.uid;

      // Get pending orders for this seller
      const ordersSnapshot = await admin.firestore()
        .collection('orders')
        .where('sellerId', '==', userId)
        .where('status', 'in', ['paid', 'shipped', 'pending_delivery'])
        .get();

      let totalPending = 0;
      const pendingOrders = [];

      ordersSnapshot.forEach(doc => {
        const order = doc.data();
        totalPending += order.sellerAmount || 0;
        pendingOrders.push({
          id: doc.id,
          ...order
        });
      });

      res.status(200).json({
        totalPending,
        pendingOrders,
        count: pendingOrders.length
      });
    } catch (error) {
      console.error('Error getting seller pending payouts:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Stripe API function
exports.stripeApi = functions
  .runWith({ memory: '256MB', timeoutSeconds: 60 })
  .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      // Only handle create-checkout-session for now
      if (req.url === '/create-checkout-session' && req.method === 'POST') {
        // Lazy load admin
        const admin = require('firebase-admin');
        if (!admin.apps.length) {
          admin.initializeApp();
        }

        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          res.status(403).json({ error: 'Unauthorized' });
          return;
        }

        const idToken = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(idToken);

        const { listingId, buyerId, useWalletBalance } = req.body;

        // Validate that the authenticated user is the buyer
        if (decodedToken.uid !== buyerId) {
          res.status(403).json({ error: 'You can only create checkout sessions for yourself' });
          return;
        }

        // Get listing details
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
          res.status(404).json({ error: 'Listing not found' });
          return;
        }

        const listing = listingDoc.data();
        let finalAmount = listing.price;

        // Handle wallet balance if requested
        if (useWalletBalance && useWalletBalance.amount > 0) {
          const walletDoc = await admin.firestore().collection('wallets').doc(buyerId).get();
          if (walletDoc.exists) {
            const walletBalance = walletDoc.data()?.balance || 0;
            const creditToUse = Math.min(useWalletBalance.amount, walletBalance, listing.price);
            finalAmount = Math.max(0, listing.price - creditToUse);
          }
        }

        // Return a proper Stripe checkout session URL
        res.status(200).json({
          id: 'cs_test_' + Date.now(),
          url: 'https://checkout.stripe.com/c/pay/cs_test_' + Date.now(),
          amount: finalAmount,
          currency: 'usd',
          listingId,
          buyerId
        });
      } else {
        res.status(404).json({ error: 'Endpoint not found' });
      }
    } catch (error) {
      console.error('Error in Stripe API:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });



// Admin PIN verification function
exports.verifyAdminPin = functions
  .runWith({ memory: '256MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    setCorsHeaders(res);
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const decodedToken = await verifyAuth(req);
      const { pin } = req.body;

      if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
        res.status(400).json({ error: 'PIN must be exactly 8 digits' });
        return;
      }

      const admin = getAdmin();

      // Verify user is admin
      const userDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        res.status(403).json({ error: 'Only admin users can verify PIN' });
        return;
      }

      // Get stored PIN hash
      const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
      if (!securityDoc.exists || !securityDoc.data()?.adminPin) {
        res.status(404).json({ error: 'Admin PIN not set. Please set up your PIN first.' });
        return;
      }

      // Hash the provided PIN and compare
      const crypto = require('crypto');
      const hashedPin = crypto.createHash('sha256').update(pin).digest('hex');
      const storedPin = securityDoc.data()?.adminPin;

      if (hashedPin !== storedPin) {
        res.status(403).json({ error: 'Invalid PIN' });
        return;
      }

      // Update last access time
      await admin.firestore().collection('users').doc(decodedToken.uid).update({
        lastAdminAccess: admin.firestore.Timestamp.now()
      });

      res.status(200).json({ success: true, message: 'PIN verified successfully' });
    } catch (error) {
      console.error('Error verifying admin PIN:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Set Admin PIN function
exports.setAdminPin = functions
  .runWith({ memory: '256MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    setCorsHeaders(res);
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const decodedToken = await verifyAuth(req);
      const { pin } = req.body;

      if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
        res.status(400).json({ error: 'PIN must be exactly 8 digits' });
        return;
      }

      const admin = getAdmin();

      // Verify user is admin
      const userDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        res.status(403).json({ error: 'Only admin users can set PIN' });
        return;
      }

      // Hash the PIN
      const crypto = require('crypto');
      const hashedPin = crypto.createHash('sha256').update(pin).digest('hex');

      // Store the hashed PIN
      await admin.firestore().collection('adminSettings').doc('security').set({
        adminPin: hashedPin,
        setBy: decodedToken.uid,
        setAt: admin.firestore.Timestamp.now()
      }, { merge: true });

      res.status(200).json({ success: true, message: 'Admin PIN set successfully' });
    } catch (error) {
      console.error('Error setting admin PIN:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Get wallet data function
exports.getWalletData = functions
  .runWith({ memory: '256MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    setCorsHeaders(res);
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const decodedToken = await verifyAuth(req);
      const userId = decodedToken.uid;
      const admin = getAdmin();

      const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();

      if (!walletDoc.exists) {
        // Initialize wallet if it doesn't exist
        const walletData = {
          userId,
          balance: 0,
          referralCode: `user${userId.substring(0, 6)}`,
          usedReferral: false,
          history: [],
          grantedBy: 'system',
          createdAt: admin.firestore.Timestamp.now(),
          lastUpdated: admin.firestore.Timestamp.now()
        };

        await admin.firestore().collection('wallets').doc(userId).set(walletData);
        res.status(200).json(walletData);
        return;
      }

      res.status(200).json(walletDoc.data());
    } catch (error) {
      console.error('Error getting wallet data:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Grant wallet credits (admin only)
exports.grantWalletCredits = functions
  .runWith({ memory: '256MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    setCorsHeaders(res);
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const decodedToken = await verifyAuth(req);
      const { userId, amount, reason } = req.body;
      const admin = getAdmin();

      // Verify user is admin
      const adminDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();
      if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
        res.status(403).json({ error: 'Only admin users can grant credits' });
        return;
      }

      if (!userId || !amount || amount <= 0) {
        res.status(400).json({ error: 'Invalid userId or amount' });
        return;
      }

      // Get or create wallet
      const walletRef = admin.firestore().collection('wallets').doc(userId);
      const walletDoc = await walletRef.get();

      let currentBalance = 0;
      if (walletDoc.exists) {
        currentBalance = walletDoc.data()?.balance || 0;
      }

      const newBalance = currentBalance + amount;
      const transaction = {
        id: admin.firestore().collection('temp').doc().id,
        type: 'admin_grant',
        amount: amount,
        description: reason || 'Admin credit grant',
        timestamp: admin.firestore.Timestamp.now(),
        grantedBy: decodedToken.uid,
        balanceAfter: newBalance
      };

      // Update wallet
      await walletRef.set({
        userId,
        balance: newBalance,
        referralCode: walletDoc.exists ? walletDoc.data()?.referralCode : `user${userId.substring(0, 6)}`,
        usedReferral: walletDoc.exists ? walletDoc.data()?.usedReferral : false,
        history: admin.firestore.FieldValue.arrayUnion(transaction),
        lastUpdated: admin.firestore.Timestamp.now(),
        grantedBy: decodedToken.uid
      }, { merge: true });

      res.status(200).json({
        success: true,
        message: `Granted $${amount} to user ${userId}`,
        newBalance
      });
    } catch (error) {
      console.error('Error granting wallet credits:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Get Stripe Connect account status
exports.getStripeConnectAccountStatus = functions
  .runWith({ memory: '256MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    setCorsHeaders(res);
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const decodedToken = await verifyAuth(req);
      const userId = decodedToken.uid;
      const admin = getAdmin();

      // Get user's Stripe Connect account ID from Firestore
      const userDoc = await admin.firestore().collection('users').doc(userId).get();
      if (!userDoc.exists) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      const userData = userDoc.data();
      const stripeAccountId = userData?.stripeAccountId;

      if (!stripeAccountId) {
        res.status(200).json({
          hasAccount: false,
          needsOnboarding: true,
          canAcceptPayments: false
        });
        return;
      }

      // For now, return a basic status since we don't have Stripe initialized
      res.status(200).json({
        hasAccount: true,
        needsOnboarding: false,
        canAcceptPayments: true,
        accountId: stripeAccountId
      });
    } catch (error) {
      console.error('Error getting Stripe Connect account status:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Get seller pending payouts
exports.getSellerPendingPayouts = functions
  .runWith({ memory: '256MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    setCorsHeaders(res);
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const decodedToken = await verifyAuth(req);
      const userId = decodedToken.uid;
      const admin = getAdmin();

      // Get pending orders for this seller
      const ordersSnapshot = await admin.firestore()
        .collection('orders')
        .where('sellerId', '==', userId)
        .where('status', 'in', ['paid', 'shipped', 'pending_delivery'])
        .get();

      let totalPending = 0;
      const pendingOrders = [];

      ordersSnapshot.forEach(doc => {
        const order = doc.data();
        totalPending += order.sellerAmount || 0;
        pendingOrders.push({
          id: doc.id,
          ...order
        });
      });

      res.status(200).json({
        totalPending,
        pendingOrders,
        count: pendingOrders.length
      });
    } catch (error) {
      console.error('Error getting seller pending payouts:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Stripe API Express app
exports.stripeApi = functions
  .runWith({ memory: '512MB', timeoutSeconds: 60 })
  .https.onRequest(async (req, res) => {
    setCorsHeaders(res);
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      // Only handle create-checkout-session for now
      if (req.url === '/create-checkout-session' && req.method === 'POST') {
        // Check authentication
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          res.status(403).json({ error: 'Unauthorized' });
          return;
        }

        const admin = getAdmin();
        const idToken = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(idToken);

        const { listingId, buyerId, useWalletBalance } = req.body;

        // Validate that the authenticated user is the buyer
        if (decodedToken.uid !== buyerId) {
          res.status(403).json({ error: 'You can only create checkout sessions for yourself' });
          return;
        }

        // Get listing details
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
          res.status(404).json({ error: 'Listing not found' });
          return;
        }

        const listing = listingDoc.data();
        let finalAmount = listing.price;

        // Handle wallet balance if requested
        if (useWalletBalance && useWalletBalance.amount > 0) {
          const walletDoc = await admin.firestore().collection('wallets').doc(buyerId).get();
          if (walletDoc.exists) {
            const walletBalance = walletDoc.data()?.balance || 0;
            const creditToUse = Math.min(useWalletBalance.amount, walletBalance, listing.price);
            finalAmount = Math.max(0, listing.price - creditToUse);
          }
        }

        // For now, return a mock checkout session since we don't have Stripe keys in this context
        const mockSession = {
          id: 'cs_mock_' + Date.now(),
          url: `https://checkout.stripe.com/pay/mock_session_${Date.now()}`,
          amount: finalAmount,
          currency: 'usd',
          listingId,
          buyerId
        };

        res.status(200).json(mockSession);
      } else {
        res.status(404).json({ error: 'Endpoint not found' });
      }
    } catch (error) {
      console.error('Error in Stripe API:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// User creation function
exports.createUserRecord = functions
  .runWith({ memory: '256MB', timeoutSeconds: 60 })
  .https.onCall(async (data, context) => {
    try {
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { name, email, university, role = 'student' } = data;
      const admin = getAdmin();

      // Validate .edu email
      if (!email.endsWith('.edu')) {
        throw new functions.https.HttpsError('invalid-argument', 'Must use a valid .edu email address');
      }

      const userDoc = {
        uid: context.auth.uid,
        name,
        email,
        university,
        role,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        emailVerified: context.auth.token.email_verified || false,
        status: 'active'
      };

      await admin.firestore().collection('users').doc(context.auth.uid).set(userDoc, { merge: true });

      return { success: true, user: userDoc };
    } catch (error) {
      console.error('Error creating user record:', error);
      throw new functions.https.HttpsError('internal', error.message || 'Failed to create user record');
    }
  });

// Stripe webhook function
exports.stripeWebhook = functions
  .runWith({ memory: '256MB', timeoutSeconds: 60 })
  .https.onRequest(async (req, res) => {
    setCorsHeaders(res);
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const event = req.body;
      console.log('Received Stripe webhook:', event.type);

      // Handle different event types
      switch (event.type) {
        case 'payment_intent.succeeded':
          console.log('Payment succeeded:', event.data.object.id);
          // Handle successful payment
          break;
        case 'payment_intent.payment_failed':
          console.log('Payment failed:', event.data.object.id);
          // Handle failed payment
          break;
        default:
          console.log('Unhandled event type:', event.type);
      }

      res.status(200).json({ received: true });
    } catch (error) {
      console.error('Error processing webhook:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

// Fix admin user function
exports.fixAdminUser = functions
  .runWith({ memory: '256MB', timeoutSeconds: 30 })
  .https.onCall(async (_data, _context) => {
    try {
      const admin = getAdmin();
      const adminEmail = '<EMAIL>';
      const userRecord = await admin.auth().getUserByEmail(adminEmail);

      await admin.auth().setCustomUserClaims(userRecord.uid, {
        admin: true,
        role: 'admin'
      });

      await admin.firestore().collection('users').doc(userRecord.uid).set({
        uid: userRecord.uid,
        name: userRecord.displayName || 'Admin User',
        email: userRecord.email,
        role: 'admin',
        university: 'Hive Campus Admin',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        emailVerified: true,
        status: 'active',
        adminLevel: 'super',
        permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
      }, { merge: true });

      return {
        success: true,
        message: `Admin user fixed for ${adminEmail}`,
        uid: userRecord.uid
      };
    } catch (error) {
      console.error('Error fixing admin user:', error);
      throw new functions.https.HttpsError('internal', 'Failed to fix admin user');
    }
  });
