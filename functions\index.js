const functions = require('firebase-functions/v1');

exports.testFunction = functions.https.onRequest((req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  res.json({
    success: true,
    message: 'Hive Campus Functions deployed successfully - CORS Fixed',
    timestamp: new Date().toISOString(),
    version: '8.3.0-ULTRA-MINIMAL'
  });
});

exports.verifyAdminPin = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    if (req.method !== 'POST') {
      res.status(405).json({ error: 'Method not allowed' });
      return;
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    const { pin } = req.body;

    if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
      res.status(400).json({ error: 'PIN must be exactly 8 digits' });
      return;
    }

    // Verify user is admin
    const userDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      res.status(403).json({ error: 'Only admin users can verify PIN' });
      return;
    }

    // Get stored PIN hash
    const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
    if (!securityDoc.exists || !securityDoc.data()?.adminPin) {
      res.status(404).json({ error: 'Admin PIN not set. Please set up your PIN first.' });
      return;
    }

    // Hash the provided PIN and compare
    const crypto = require('crypto');
    const hashedPin = crypto.createHash('sha256').update(pin).digest('hex');
    const storedPin = securityDoc.data()?.adminPin;

    if (hashedPin !== storedPin) {
      res.status(403).json({ error: 'Invalid PIN' });
      return;
    }

    res.status(200).json({ success: true, message: 'PIN verified successfully' });
  } catch (error) {
    console.error('Error verifying admin PIN:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});

exports.getWalletData = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    if (req.method !== 'POST') {
      res.status(405).json({ error: 'Method not allowed' });
      return;
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    const userId = decodedToken.uid;

    const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();

    if (!walletDoc.exists) {
      const walletData = {
        userId,
        balance: 0,
        referralCode: `user${userId.substring(0, 6)}`,
        usedReferral: false,
        history: [],
        grantedBy: 'system',
        createdAt: admin.firestore.Timestamp.now(),
        lastUpdated: admin.firestore.Timestamp.now()
      };

      await admin.firestore().collection('wallets').doc(userId).set(walletData);
      res.status(200).json(walletData);
      return;
    }

    res.status(200).json(walletDoc.data());
  } catch (error) {
    console.error('Error getting wallet data:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});

exports.getStripeConnectAccountStatus = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    if (req.method !== 'POST') {
      res.status(405).json({ error: 'Method not allowed' });
      return;
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    const userId = decodedToken.uid;

    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    if (!userDoc.exists) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    const userData = userDoc.data();
    const stripeAccountId = userData?.stripeAccountId;

    if (!stripeAccountId) {
      res.status(200).json({
        hasAccount: false,
        needsOnboarding: true,
        canAcceptPayments: false
      });
      return;
    }

    res.status(200).json({
      hasAccount: true,
      needsOnboarding: false,
      canAcceptPayments: true,
      accountId: stripeAccountId
    });
  } catch (error) {
    console.error('Error getting Stripe Connect account status:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});

// Grant wallet credits (admin only)
exports.grantWalletCredits = functions
  .runWith({ memory: '128MB', timeoutSeconds: 30 })
  .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Lazy load admin
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      const { userId, amount, description } = req.body;

      // Verify user is admin
      const adminDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();
      if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
        res.status(403).json({ error: 'Only admin users can grant credits' });
        return;
      }

      if (!userId || !amount || amount <= 0) {
        res.status(400).json({ error: 'Invalid userId or amount' });
        return;
      }

      // Get or create wallet
      const walletRef = admin.firestore().collection('wallets').doc(userId);
      const walletDoc = await walletRef.get();

      let currentBalance = 0;
      if (walletDoc.exists) {
        currentBalance = walletDoc.data()?.balance || 0;
      }

      const newBalance = currentBalance + amount;
      const transaction = {
        id: admin.firestore().collection('temp').doc().id,
        type: 'admin_grant',
        amount: amount,
        description: description || 'Admin credit grant',
        timestamp: admin.firestore.Timestamp.now(),
        grantedBy: decodedToken.uid,
        balanceAfter: newBalance
      };

      // Update wallet
      await walletRef.set({
        userId,
        balance: newBalance,
        referralCode: walletDoc.exists ? walletDoc.data()?.referralCode : `user${userId.substring(0, 6)}`,
        usedReferral: walletDoc.exists ? walletDoc.data()?.usedReferral : false,
        history: admin.firestore.FieldValue.arrayUnion(transaction),
        lastUpdated: admin.firestore.Timestamp.now(),
        grantedBy: decodedToken.uid
      }, { merge: true });

      res.status(200).json({
        success: true,
        message: `Granted $${amount} to user ${userId}`,
        newBalance
      });
    } catch (error) {
      console.error('Error granting wallet credits:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });

exports.getSellerPendingPayouts = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    if (req.method !== 'POST') {
      res.status(405).json({ error: 'Method not allowed' });
      return;
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    const userId = decodedToken.uid;

    const ordersSnapshot = await admin.firestore()
      .collection('orders')
      .where('sellerId', '==', userId)
      .where('status', 'in', ['paid', 'shipped', 'pending_delivery'])
      .get();

    let totalPending = 0;
    const pendingOrders = [];

    ordersSnapshot.forEach(doc => {
      const order = doc.data();
      totalPending += order.sellerAmount || 0;
      pendingOrders.push({
        id: doc.id,
        ...order
      });
    });

    res.status(200).json({
      totalPending,
      pendingOrders,
      count: pendingOrders.length
    });
  } catch (error) {
    console.error('Error getting seller pending payouts:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});

exports.createUserRecord = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { name, email, university, role = 'student' } = data;
    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    if (!email.endsWith('.edu')) {
      throw new functions.https.HttpsError('invalid-argument', 'Must use a valid .edu email address');
    }

    const userDoc = {
      uid: context.auth.uid,
      name,
      email,
      university,
      role,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: context.auth.token.email_verified || false,
      status: 'active'
    };

    await admin.firestore().collection('users').doc(context.auth.uid).set(userDoc, { merge: true });

    return { success: true, user: userDoc };
  } catch (error) {
    console.error('Error creating user record:', error);
    throw new functions.https.HttpsError('internal', error.message || 'Failed to create user record');
  }
});
