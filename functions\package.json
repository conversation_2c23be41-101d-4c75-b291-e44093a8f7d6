{"name": "functions", "scripts": {"build": "echo 'Using pure JavaScript - no build needed'", "build:clean": "echo 'Using pure JavaScript - no build needed'", "build:watch": "echo 'Using pure JavaScript - no build needed'", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "deploy:minimal": "firebase deploy --only functions:testFunction", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "index.js", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "firebase-admin": "^12.0.0", "firebase-functions": "^6.4.0", "stripe": "^14.0.0"}, "devDependencies": {"typescript": "^5.8.3"}, "private": true}